# TTM Squeeze Pattern Detection - Technical Implementation Guide

## Overview

This document provides a detailed technical explanation of the TTM Squeeze pattern detection algorithm implemented in `atlas_ttm_pattern_detector.py`. The implementation focuses on precise pattern recognition based on histogram momentum analysis with multi-timeframe confirmation.

## Core Algorithm Components

### 1. Histogram Momentum Pattern Detection

#### Mathematical Logic
The algorithm detects a specific 4-bar histogram pattern:

```python
# Pattern Criteria:
# Bar 1 > Bar 2 > Bar 3 (decreasing sequence)
# Bar 4 > Bar 3 (momentum shift confirmation)

def validate_pattern(bars):
    decreasing_sequence = (bars[0] > bars[1] and bars[1] > bars[2])
    momentum_shift = bars[3] > bars[2]
    return decreasing_sequence and momentum_shift
```

#### Histogram Calculation
The TTM Histogram uses linear regression slope of closing prices:

```python
def calculate_histogram(close_prices, period=12):
    def linear_regression_slope(series):
        x = np.arange(len(series))
        y = series.values
        n = len(x)
        
        # Linear regression formula
        slope = (n * sum(x*y) - sum(x) * sum(y)) / (n * sum(x²) - sum(x)²)
        return slope
    
    return close_prices.rolling(period).apply(linear_regression_slope)
```

#### Pattern Confidence Scoring
```python
def calculate_pattern_confidence(pattern):
    confidence = 0.0
    
    # Base confidence for decreasing sequence (40%)
    if pattern.decreasing_sequence:
        confidence += 0.4
        
        # Consistency bonus (20%)
        decrease_1 = abs(bars[0] - bars[1])
        decrease_2 = abs(bars[1] - bars[2])
        consistency = 1.0 - abs(decrease_1 - decrease_2) / max(decrease_1 + decrease_2, 0.001)
        confidence += consistency * 0.2
    
    # Momentum shift confirmation (30%)
    if pattern.momentum_shift:
        confidence += 0.3
        
        # Strength bonus (10%)
        strength_factor = min(pattern.momentum_strength * 10, 0.1)
        confidence += strength_factor
    
    return min(confidence, 1.0)
```

### 2. Multi-Timeframe Analysis

#### Timeframe Alignment Logic
```python
def check_timeframe_alignment(daily_pattern, weekly_pattern):
    # Both patterns must be valid
    daily_valid = daily_pattern.decreasing_sequence and daily_pattern.momentum_shift
    weekly_valid = weekly_pattern.decreasing_sequence and weekly_pattern.momentum_shift
    
    if not (daily_valid and weekly_valid):
        return False
    
    # Check directional alignment
    daily_direction = 1 if daily_pattern.bars[-1] > 0 else -1
    weekly_direction = 1 if weekly_pattern.bars[-1] > 0 else -1
    
    return daily_direction == weekly_direction
```

#### Combined Confidence Calculation
```python
def calculate_combined_confidence(daily_pattern, weekly_pattern, alignment):
    # Weight daily more heavily (70%) than weekly (30%)
    combined = (daily_pattern.confidence * 0.7 + 
               weekly_pattern.confidence * 0.3)
    
    # Alignment bonus (10%)
    if alignment:
        combined += 0.1
    
    return min(combined, 1.0)
```

### 3. Squeeze State Detection (Optional Enhancement)

#### Squeeze Condition
```python
def detect_squeeze_state(bb_upper, bb_lower, kc_upper, kc_lower):
    # Bollinger Bands inside Keltner Channels
    squeeze_active = (bb_upper <= kc_upper) & (bb_lower >= kc_lower)
    return squeeze_active
```

#### Squeeze Intensity Calculation
```python
def calculate_squeeze_intensity(bb_width, kc_width):
    if kc_width > 0:
        intensity = 1.0 - (bb_width / kc_width)
        return max(0.0, min(1.0, intensity))
    return 0.0
```

#### Release Probability
```python
def calculate_release_probability(squeeze_duration, squeeze_intensity):
    # Longer squeezes have higher release probability
    duration_factor = min(squeeze_duration / 20.0, 0.5)
    
    # Tighter squeezes have higher release probability  
    intensity_factor = squeeze_intensity * 0.3
    
    return min(duration_factor + intensity_factor, 0.8)
```

## Technical Indicators

### Bollinger Bands
```python
def calculate_bollinger_bands(close_prices, period=20, std_dev=2.0):
    middle = close_prices.rolling(period).mean()
    std = close_prices.rolling(period).std()
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    return upper, middle, lower
```

### Keltner Channels
```python
def calculate_keltner_channels(close_prices, high_prices, low_prices, period=20, multiplier=1.5):
    middle = close_prices.rolling(period).mean()
    
    # True Range calculation
    tr1 = high_prices - low_prices
    tr2 = abs(high_prices - close_prices.shift(1))
    tr3 = abs(low_prices - close_prices.shift(1))
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    atr = true_range.rolling(period).mean()
    upper = middle + (atr * multiplier)
    lower = middle - (atr * multiplier)
    
    return upper, middle, lower
```

## Signal Confidence Calculation

### Overall Signal Confidence
```python
def calculate_signal_confidence(daily_pattern, multi_tf_analysis, squeeze_state):
    confidence = 0.0
    
    # Daily pattern confidence (60% weight)
    confidence += daily_pattern.confidence * 0.6
    
    # Multi-timeframe bonus (25% weight)
    if multi_tf_analysis and multi_tf_analysis.alignment:
        confidence += multi_tf_analysis.combined_confidence * 0.25
    else:
        confidence += daily_pattern.confidence * 0.15
    
    # Squeeze state bonus (15% weight)
    if squeeze_state.is_squeezed:
        squeeze_bonus = squeeze_state.release_probability * 0.15
        confidence += squeeze_bonus
    
    return min(confidence, 1.0)
```

### Signal Strength Mapping
```python
def map_confidence_to_strength(confidence):
    if confidence >= 0.9:
        return SignalStrength.VERY_STRONG
    elif confidence >= 0.8:
        return SignalStrength.STRONG
    elif confidence >= 0.7:
        return SignalStrength.MODERATE
    elif confidence >= 0.6:
        return SignalStrength.WEAK
    else:
        return SignalStrength.VERY_WEAK
```

## Configuration Parameters

### Pattern Detection Thresholds
```python
# Bollinger Bands
BB_PERIOD = 20
BB_STDDEV = 2.0

# Keltner Channels  
KC_PERIOD = 20
KC_MULTIPLIER = 1.5

# Momentum/Histogram
MOMENTUM_PERIOD = 12

# Pattern Validation
MIN_PATTERN_CONFIDENCE = 0.6
MIN_MOMENTUM_SHIFT = 0.001
SQUEEZE_INTENSITY_THRESHOLD = 0.8
```

### Timeframe Weights
```python
# Multi-timeframe analysis weights
DAILY_WEIGHT = 0.7
WEEKLY_WEIGHT = 0.3
ALIGNMENT_BONUS = 0.1

# Signal confidence weights
PATTERN_WEIGHT = 0.6
TIMEFRAME_WEIGHT = 0.25
SQUEEZE_WEIGHT = 0.15
```

## Integration with Market Engine

### Usage Example
```python
from atlas_ttm_pattern_detector import ttm_pattern_integrator

# Get enhanced TTM signal
signal = await ttm_pattern_integrator.get_enhanced_ttm_signal("AAPL")

if signal:
    print(f"Signal: {signal.symbol}")
    print(f"Direction: {signal.momentum_direction}")
    print(f"Strength: {signal.signal_strength}")
    print(f"Confidence: {signal.confidence}")
    print(f"Entry: ${signal.entry_price}")
    print(f"Stop: ${signal.stop_loss}")
    print(f"Target: ${signal.target_price}")
```

### Market Engine Integration
```python
# In atlas_market_engine.py
from atlas_ttm_pattern_detector import ttm_pattern_integrator

class AtlasMarketEngine:
    def __init__(self):
        # Initialize pattern integrator with market engine reference
        ttm_pattern_integrator.market_engine = self
    
    async def get_ttm_squeeze_signal(self, symbol: str):
        # Use enhanced pattern detection
        return await ttm_pattern_integrator.get_enhanced_ttm_signal(symbol)
```

## Performance Considerations

### Caching Strategy
- Pattern results cached for 5 minutes
- Cache size limited to 100 symbols
- LRU eviction for memory management

### Computational Complexity
- O(n) for indicator calculations where n = data points
- O(1) for pattern validation (fixed 4-bar analysis)
- Total complexity: O(n) per symbol

### Optimization Features
- Lazy loading of weekly data
- Graceful fallback for missing data
- Vectorized calculations using pandas/numpy
- Performance monitoring with decorators

## Error Handling

### Graceful Degradation
1. **Missing Weekly Data**: Falls back to daily-only analysis
2. **Insufficient Data**: Returns None instead of invalid signals
3. **Calculation Errors**: Logs errors and continues with fallback methods
4. **API Failures**: Uses cached data when available

### Validation Checks
- Minimum data requirements (50+ bars for daily, 30+ for weekly)
- NaN value handling in calculations
- Parameter bounds checking
- Signal confidence thresholds

This implementation provides a robust, mathematically sound approach to TTM Squeeze pattern detection with comprehensive error handling and performance optimization.
