# A.T.L.A.S Enhanced Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the enhanced A.T.L.A.S trading system with all migrated features from CHatbotfinal.

## Prerequisites

### System Requirements
- Python 3.9+
- 8GB+ RAM (16GB recommended for ML features)
- 50GB+ storage space
- Internet connection for API access

### Required API Keys
```bash
# Core APIs
OPENAI_API_KEY=your_openai_key
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret

# Enhanced Features (Optional)
GOOGLE_SEARCH_API_KEY=your_google_search_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
TWITTER_BEARER_TOKEN=your_twitter_token
REDDIT_CLIENT_ID=your_reddit_id
REDDIT_CLIENT_SECRET=your_reddit_secret
```

## Installation Steps

### 1. Environment Setup
```bash
# Clone repository
git clone <repository_url>
cd atlas_rebuilt

# Create virtual environment
python -m venv atlas_env
source atlas_env/bin/activate  # Linux/Mac
# or
atlas_env\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env  # Add your API keys and settings
```

### 3. Database Initialization
```bash
# Initialize all databases
python -c "
import asyncio
from atlas_database_manager import AtlasDatabaseManager

async def init_db():
    db = AtlasDatabaseManager()
    await db.initialize()
    print('✅ All databases initialized')

asyncio.run(init_db())
"
```

### 4. ML Models Setup (Optional)
```bash
# Download ML models (if using ML features)
python -c "
import asyncio
from atlas_sentiment_analyzer import sentiment_analyzer
from atlas_ml_predictor import ml_predictor

async def init_ml():
    await sentiment_analyzer.initialize()
    await ml_predictor.initialize()
    print('✅ ML models initialized')

asyncio.run(init_ml())
"
```

## Configuration Options

### Core Settings
```python
# config.py - Core settings
ATLAS_MODE = "production"  # or "development"
LOG_LEVEL = "INFO"
MAX_SCAN_RESULTS = 50
CACHE_TTL = 300

# Performance settings
PERFORMANCE_MONITORING_ENABLED = True
MEMORY_USAGE_THRESHOLD = 80.0
CPU_USAGE_THRESHOLD = 80.0
```

### Enhanced Features
```python
# ML and AI Features
ML_MODELS_ENABLED = True
SENTIMENT_MODEL_PATH = "distilbert-base-uncased-finetuned-sst-2-english"
ML_PREDICTION_CONFIDENCE_THRESHOLD = 0.7

# Options Trading
OPTIONS_TRADING_ENABLED = True
OPTIONS_MAX_EXPIRY_DAYS = 45
OPTIONS_MIN_VOLUME = 100
OPTIONS_MAX_SPREAD_PERCENT = 5.0

# Proactive Assistant
PROACTIVE_ASSISTANT_ENABLED = True
MORNING_BRIEFING_TIME = "09:00"
ALERT_COOLDOWN_MINUTES = 15
MIN_SIGNAL_STRENGTH = 4
```

## Deployment Options

### Option 1: Development Mode
```bash
# Start development server
python main.py --mode development

# Or with specific components
python main.py --enable-ml --enable-options --enable-proactive
```

### Option 2: Production Mode
```bash
# Start production server
python main.py --mode production

# With all features enabled
python main.py --mode production --enable-all
```

### Option 3: Docker Deployment
```bash
# Build Docker image
docker build -t atlas-enhanced .

# Run container
docker run -d \
  --name atlas-trading \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/.env:/app/.env \
  atlas-enhanced
```

### Option 4: Cloud Deployment
```bash
# AWS deployment example
aws ecs create-cluster --cluster-name atlas-cluster
aws ecs register-task-definition --cli-input-json file://task-definition.json
aws ecs create-service --cluster atlas-cluster --service-name atlas-service
```

## Feature Activation

### Enable All Features
```python
# In config.py or environment variables
ML_MODELS_ENABLED = True
OPTIONS_TRADING_ENABLED = True
PROACTIVE_ASSISTANT_ENABLED = True
ENHANCED_MEMORY_ENABLED = True
PERFORMANCE_MONITORING_ENABLED = True
```

### Selective Feature Activation
```python
# Enable only specific features
ML_MODELS_ENABLED = False  # Disable ML for faster startup
OPTIONS_TRADING_ENABLED = True  # Keep options trading
PROACTIVE_ASSISTANT_ENABLED = True  # Keep proactive features
```

## Testing Deployment

### 1. Run Test Suite
```bash
# Run comprehensive tests
python test_atlas_migration.py

# Expected output:
# 🎉 ALL TESTS PASSED! Migration completed successfully!
```

### 2. Health Check
```bash
# Check system health
curl http://localhost:8000/health

# Expected response:
# {
#   "status": "healthy",
#   "components": {
#     "ai_engine": "active",
#     "market_engine": "active",
#     "sentiment_analyzer": "active",
#     "ml_predictor": "active",
#     "options_engine": "active",
#     "portfolio_optimizer": "active",
#     "proactive_assistant": "active"
#   }
# }
```

### 3. Feature Testing
```bash
# Test TTM Squeeze detection
curl -X POST http://localhost:8000/api/scan \
  -H "Content-Type: application/json" \
  -d '{"symbols": ["AAPL"], "strength": "strong"}'

# Test sentiment analysis
curl -X POST http://localhost:8000/api/sentiment \
  -H "Content-Type: application/json" \
  -d '{"symbol": "AAPL"}'

# Test options analysis
curl -X POST http://localhost:8000/api/options \
  -H "Content-Type: application/json" \
  -d '{"symbol": "AAPL", "strategy": "long_call"}'
```

## Monitoring and Maintenance

### Performance Monitoring
```bash
# View performance metrics
curl http://localhost:8000/api/performance

# Monitor system resources
python -c "
from atlas_performance_optimizer import performance_optimizer
health = performance_optimizer.get_system_health()
print(f'CPU: {health[\"cpu_usage\"]}%')
print(f'Memory: {health[\"memory_usage\"]}%')
print(f'Response Time: {health[\"avg_response_time\"]}s')
"
```

### Log Monitoring
```bash
# View logs
tail -f logs/atlas.log

# Filter for errors
grep "ERROR" logs/atlas.log

# Monitor specific components
grep "sentiment_analyzer" logs/atlas.log
```

### Database Maintenance
```bash
# Check database status
python -c "
import asyncio
from atlas_database_manager import AtlasDatabaseManager

async def check_db():
    db = AtlasDatabaseManager()
    await db.initialize()
    print('✅ All databases operational')

asyncio.run(check_db())
"
```

## Troubleshooting

### Common Issues

#### 1. ML Models Not Loading
```bash
# Check ML dependencies
pip install torch transformers scikit-learn

# Verify model path
python -c "from transformers import DistilBertTokenizer; print('✅ Models accessible')"
```

#### 2. Database Connection Issues
```bash
# Check database files
ls -la *.db

# Reinitialize if needed
rm *.db
python -c "
import asyncio
from atlas_database_manager import AtlasDatabaseManager
asyncio.run(AtlasDatabaseManager().initialize())
"
```

#### 3. API Rate Limits
```bash
# Check API usage
grep "rate limit" logs/atlas.log

# Adjust rate limiting in config
RATE_LIMIT_REQUESTS = 100
RATE_LIMIT_WINDOW = 3600
```

#### 4. Memory Issues
```bash
# Monitor memory usage
python -c "
import psutil
print(f'Memory: {psutil.virtual_memory().percent}%')
"

# Disable ML features if needed
ML_MODELS_ENABLED = False
```

## Security Considerations

### API Key Security
- Store API keys in environment variables
- Use secrets management in production
- Rotate keys regularly
- Monitor API usage

### Network Security
- Use HTTPS in production
- Implement rate limiting
- Set up firewall rules
- Monitor access logs

### Data Security
- Encrypt sensitive data
- Regular database backups
- Secure file permissions
- Audit data access

## Scaling and Performance

### Horizontal Scaling
```bash
# Multiple instances with load balancer
docker-compose up --scale atlas=3
```

### Performance Optimization
```python
# Tune cache settings
CACHE_TTL = 300  # 5 minutes
MAX_CACHE_SIZE = 1000

# Adjust scan intervals
SCAN_INTERVAL = 60  # seconds
UPDATE_INTERVAL = 300  # seconds
```

### Resource Allocation
```bash
# Set memory limits
docker run --memory=8g atlas-enhanced

# CPU limits
docker run --cpus=4 atlas-enhanced
```

## Support and Maintenance

### Regular Maintenance
- Update dependencies monthly
- Monitor performance metrics
- Review and rotate logs
- Backup databases weekly
- Test disaster recovery

### Updates and Upgrades
- Test updates in staging environment
- Backup before upgrades
- Monitor post-upgrade performance
- Rollback plan ready

## Success Metrics

### Deployment Success Indicators
- ✅ All tests pass
- ✅ System health check returns "healthy"
- ✅ All components show "active" status
- ✅ Response times < 2 seconds
- ✅ Memory usage < 80%
- ✅ No critical errors in logs

### Operational Success Indicators
- ✅ TTM Squeeze signals generating
- ✅ Sentiment analysis working
- ✅ Options analysis functional
- ✅ Proactive alerts sending
- ✅ Portfolio optimization running
- ✅ Real-time scanning active

**🎉 Congratulations! Your enhanced A.T.L.A.S system is now deployed and operational!**
