"""
Comprehensive Test Suite for A.T.L.A.S Migration
Tests all migrated features and ensures system integration
"""

import asyncio
import pytest
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AtlasMigrationTester:
    """Comprehensive test suite for A.T.L.A.S migration"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = {}
        self.failed_tests = []
        self.passed_tests = []
    
    async def run_all_tests(self):
        """Run all migration tests"""
        self.logger.info("🚀 Starting A.T.L.A.S Migration Test Suite")
        self.logger.info("=" * 80)
        
        test_categories = [
            ("Core Infrastructure", self.test_core_infrastructure),
            ("Database Systems", self.test_database_systems),
            ("Performance Optimization", self.test_performance_optimization),
            ("Sentiment Analysis", self.test_sentiment_analysis),
            ("ML Prediction", self.test_ml_prediction),
            ("Options Trading", self.test_options_trading),
            ("Portfolio Optimization", self.test_portfolio_optimization),
            ("Market Context", self.test_market_context),
            ("Proactive Assistant", self.test_proactive_assistant),
            ("Real-time Scanner", self.test_realtime_scanner),
            ("TTM Pattern Detection", self.test_ttm_pattern_detection),
            ("System Integration", self.test_system_integration),
            ("Backward Compatibility", self.test_backward_compatibility)
        ]
        
        for category_name, test_method in test_categories:
            try:
                self.logger.info(f"\n📋 Testing {category_name}...")
                await test_method()
                self.test_results[category_name] = "PASSED"
                self.passed_tests.append(category_name)
                self.logger.info(f"✅ {category_name} tests PASSED")
            except Exception as e:
                self.test_results[category_name] = f"FAILED: {e}"
                self.failed_tests.append(category_name)
                self.logger.error(f"❌ {category_name} tests FAILED: {e}")
        
        # Print summary
        self._print_test_summary()
    
    async def test_core_infrastructure(self):
        """Test core infrastructure components"""
        # Test configuration enhancements
        from config import settings
        
        # Verify new configuration options exist
        assert hasattr(settings, 'ML_MODELS_ENABLED'), "ML_MODELS_ENABLED config missing"
        assert hasattr(settings, 'OPTIONS_TRADING_ENABLED'), "OPTIONS_TRADING_ENABLED config missing"
        assert hasattr(settings, 'PROACTIVE_ASSISTANT_ENABLED'), "PROACTIVE_ASSISTANT_ENABLED config missing"
        
        # Test models enhancements
        from models import ProactiveAlert, SentimentData, OptionContract, OptionsStrategy
        
        # Test model instantiation
        alert = ProactiveAlert(
            alert_type="opportunity_notification",
            priority="high",
            title="Test Alert",
            message="Test message",
            action_required=True
        )
        assert alert.alert_type == "opportunity_notification"
        
        self.logger.info("✓ Core infrastructure tests passed")
    
    async def test_database_systems(self):
        """Test enhanced database systems"""
        from atlas_database_manager import AtlasDatabaseManager
        
        db_manager = AtlasDatabaseManager()
        
        # Test multiple database initialization
        await db_manager.initialize()
        
        # Test database connections
        async with db_manager.get_connection('main') as conn:
            assert conn is not None, "Main database connection failed"
        
        async with db_manager.get_connection('memory') as conn:
            assert conn is not None, "Memory database connection failed"
        
        self.logger.info("✓ Database systems tests passed")
    
    async def test_performance_optimization(self):
        """Test performance optimization module"""
        from atlas_performance_optimizer import performance_optimizer
        
        # Test performance monitoring decorator
        @performance_optimizer.performance_monitor("test_operation")
        async def test_operation():
            await asyncio.sleep(0.1)
            return "success"
        
        result = await test_operation()
        assert result == "success", "Performance monitoring failed"
        
        # Test system health
        health = performance_optimizer.get_system_health()
        assert 'status' in health, "System health check failed"
        
        self.logger.info("✓ Performance optimization tests passed")
    
    async def test_sentiment_analysis(self):
        """Test sentiment analysis module"""
        try:
            from atlas_sentiment_analyzer import sentiment_analyzer
            
            await sentiment_analyzer.initialize()
            
            # Test sentiment analysis
            result = await sentiment_analyzer.analyze_sentiment("AAPL")
            
            if result:
                assert hasattr(result, 'overall_sentiment'), "Sentiment result missing overall_sentiment"
                assert hasattr(result, 'confidence'), "Sentiment result missing confidence"
                assert hasattr(result, 'signal_strength'), "Sentiment result missing signal_strength"
            
            self.logger.info("✓ Sentiment analysis tests passed")
        except ImportError:
            self.logger.warning("⚠️ Sentiment analysis module not available (optional)")
    
    async def test_ml_prediction(self):
        """Test ML prediction module"""
        try:
            from atlas_ml_predictor import ml_predictor
            
            await ml_predictor.initialize()
            
            # Test ML prediction
            result = await ml_predictor.predict_returns("AAPL")
            
            if result:
                assert hasattr(result, 'predicted_return'), "ML result missing predicted_return"
                assert hasattr(result, 'confidence'), "ML result missing confidence"
                assert hasattr(result, 'signal_strength'), "ML result missing signal_strength"
            
            self.logger.info("✓ ML prediction tests passed")
        except ImportError:
            self.logger.warning("⚠️ ML prediction module not available (optional)")
    
    async def test_options_trading(self):
        """Test options trading engine"""
        try:
            from atlas_options_engine import options_engine
            
            # Test options analysis
            result = await options_engine.analyze_options_opportunity("AAPL")
            
            if result:
                assert 'symbol' in result, "Options result missing symbol"
                assert 'strategy' in result, "Options result missing strategy"
                assert 'risk_assessment' in result, "Options result missing risk_assessment"
            
            self.logger.info("✓ Options trading tests passed")
        except ImportError:
            self.logger.warning("⚠️ Options trading module not available (optional)")
    
    async def test_portfolio_optimization(self):
        """Test portfolio optimization module"""
        try:
            from atlas_portfolio_optimizer import portfolio_optimizer
            
            await portfolio_optimizer.initialize()
            
            # Test portfolio optimization
            result = await portfolio_optimizer.optimize_portfolio(
                current_positions=[],
                target_symbols=['AAPL', 'MSFT', 'GOOGL']
            )
            
            if result:
                assert hasattr(result, 'optimal_weights'), "Portfolio result missing optimal_weights"
                assert hasattr(result, 'expected_return'), "Portfolio result missing expected_return"
                assert hasattr(result, 'sharpe_ratio'), "Portfolio result missing sharpe_ratio"
            
            self.logger.info("✓ Portfolio optimization tests passed")
        except ImportError:
            self.logger.warning("⚠️ Portfolio optimization module not available (optional)")
    
    async def test_market_context(self):
        """Test market context engine"""
        try:
            from atlas_market_context import market_context_engine
            
            # Test market context
            context = await market_context_engine.get_current_context()
            
            if context:
                assert hasattr(context, 'regime'), "Market context missing regime"
                assert hasattr(context, 'sentiment_score'), "Market context missing sentiment_score"
                assert hasattr(context, 'volatility_percentile'), "Market context missing volatility_percentile"
            
            self.logger.info("✓ Market context tests passed")
        except ImportError:
            self.logger.warning("⚠️ Market context module not available (optional)")
    
    async def test_proactive_assistant(self):
        """Test proactive assistant"""
        try:
            from atlas_proactive_assistant import proactive_assistant
            
            # Test assistant status
            status = proactive_assistant.get_assistant_status()
            assert 'enabled' in status, "Assistant status missing enabled field"
            assert 'configuration' in status, "Assistant status missing configuration"
            
            self.logger.info("✓ Proactive assistant tests passed")
        except ImportError:
            self.logger.warning("⚠️ Proactive assistant module not available (optional)")
    
    async def test_realtime_scanner(self):
        """Test real-time scanner"""
        try:
            from atlas_realtime_scanner import realtime_scanner
            
            # Test scanner status
            status = realtime_scanner.get_scanner_status()
            assert 'is_running' in status, "Scanner status missing is_running field"
            assert 'scan_symbols_count' in status, "Scanner status missing scan_symbols_count"
            
            self.logger.info("✓ Real-time scanner tests passed")
        except ImportError:
            self.logger.warning("⚠️ Real-time scanner module not available (optional)")
    
    async def test_ttm_pattern_detection(self):
        """Test TTM pattern detection"""
        try:
            from atlas_ttm_pattern_detector import ttm_pattern_detector, ttm_pattern_integrator
            
            # Test pattern detector parameters
            params = ttm_pattern_detector.get_pattern_parameters()
            assert 'bb_period' in params, "Pattern detector missing bb_period"
            assert 'min_pattern_confidence' in params, "Pattern detector missing min_pattern_confidence"
            
            # Test pattern integrator
            signal = await ttm_pattern_integrator.get_enhanced_ttm_signal("AAPL")
            # Signal may be None if no pattern detected, which is acceptable
            
            self.logger.info("✓ TTM pattern detection tests passed")
        except ImportError:
            self.logger.warning("⚠️ TTM pattern detection module not available (optional)")
    
    async def test_system_integration(self):
        """Test system integration"""
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        
        # Test orchestrator initialization
        await orchestrator.initialize()
        
        # Test component status
        status = await orchestrator.get_system_status()
        assert 'components' in status, "System status missing components"
        assert 'overall_status' in status, "System status missing overall_status"
        
        # Test that enhanced components are tracked
        components = status['components']
        enhanced_components = [
            'sentiment_analyzer', 'ml_predictor', 'options_engine',
            'portfolio_optimizer', 'market_context_engine', 'proactive_assistant'
        ]
        
        for component in enhanced_components:
            assert component in components, f"Enhanced component {component} not tracked"
        
        self.logger.info("✓ System integration tests passed")
    
    async def test_backward_compatibility(self):
        """Test backward compatibility"""
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()
        
        # Test that original components still work
        ai_engine = await orchestrator._ensure_ai_engine()
        assert ai_engine is not None, "AI engine initialization failed"
        
        market_engine = await orchestrator._ensure_market_engine()
        assert market_engine is not None, "Market engine initialization failed"
        
        # Test original API endpoints still work
        response = await ai_engine.process_message("Hello", "test_session")
        assert response is not None, "AI engine message processing failed"
        
        self.logger.info("✓ Backward compatibility tests passed")
    
    def _print_test_summary(self):
        """Print comprehensive test summary"""
        self.logger.info("\n" + "=" * 80)
        self.logger.info("🎯 A.T.L.A.S MIGRATION TEST SUMMARY")
        self.logger.info("=" * 80)
        
        total_tests = len(self.test_results)
        passed_count = len(self.passed_tests)
        failed_count = len(self.failed_tests)
        
        self.logger.info(f"📊 Total Test Categories: {total_tests}")
        self.logger.info(f"✅ Passed: {passed_count}")
        self.logger.info(f"❌ Failed: {failed_count}")
        self.logger.info(f"📈 Success Rate: {(passed_count/total_tests)*100:.1f}%")
        
        if self.passed_tests:
            self.logger.info(f"\n✅ PASSED TESTS:")
            for test in self.passed_tests:
                self.logger.info(f"   • {test}")
        
        if self.failed_tests:
            self.logger.info(f"\n❌ FAILED TESTS:")
            for test in self.failed_tests:
                self.logger.info(f"   • {test}: {self.test_results[test]}")
        
        self.logger.info("\n" + "=" * 80)
        
        if failed_count == 0:
            self.logger.info("🎉 ALL TESTS PASSED! Migration completed successfully!")
        else:
            self.logger.info(f"⚠️  {failed_count} test(s) failed. Review and fix issues.")
        
        self.logger.info("=" * 80)


async def main():
    """Run the comprehensive test suite"""
    tester = AtlasMigrationTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
