"""
Test suite for TTM Squeeze Pattern Detection
Demonstrates the pattern detection algorithm with various scenarios
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

from atlas_ttm_pattern_detector import (
    TTMPatternDetector, 
    TTMPatternIntegrator,
    HistogramPattern,
    MultiTimeframeAnalysis,
    SqueezeState
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TTMPatternTester:
    """Test suite for TTM pattern detection"""
    
    def __init__(self):
        self.detector = TTMPatternDetector()
        self.integrator = TTMPatternIntegrator()
        
    def create_test_data(self, scenario: str = "valid_pattern") -> pd.DataFrame:
        """Create test data for different scenarios"""
        
        # Base data setup
        periods = 100
        dates = pd.date_range(end=datetime.now(), periods=periods, freq='D')
        np.random.seed(42)  # For reproducible tests
        
        # Generate base OHLCV data
        base_price = 100.0
        data = []
        
        for i in range(periods):
            if i == 0:
                open_price = base_price
            else:
                open_price = data[-1]['close']
            
            # Different scenarios
            if scenario == "valid_pattern":
                # Create a valid TTM pattern in the last 4 bars
                if i >= periods - 4:
                    # Simulate decreasing momentum followed by upturn
                    momentum_values = [0.5, 0.3, 0.1, 0.4]  # Decreasing then increasing
                    change = momentum_values[i - (periods - 4)] * 0.01
                else:
                    change = np.random.normal(0, 0.01)
            
            elif scenario == "squeeze_active":
                # Create tight price action (squeeze condition)
                change = np.random.normal(0, 0.005)  # Lower volatility
            
            elif scenario == "no_pattern":
                # Random walk with no clear pattern
                change = np.random.normal(0, 0.02)
            
            else:  # default
                change = np.random.normal(0, 0.015)
            
            close_price = open_price * (1 + change)
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.005)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.005)))
            volume = np.random.randint(100000, 1000000)
            
            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
        
        return pd.DataFrame(data, index=dates)
    
    def test_histogram_pattern_detection(self):
        """Test histogram pattern detection logic"""
        logger.info("🧪 Testing Histogram Pattern Detection")
        
        # Test Case 1: Valid pattern (decreasing then increasing)
        valid_bars = [0.5, 0.3, 0.1, 0.4]
        pattern = HistogramPattern(bars=valid_bars)
        
        assert pattern.decreasing_sequence == True, "Should detect decreasing sequence"
        assert pattern.momentum_shift == True, "Should detect momentum shift"
        assert pattern.pattern_confidence > 0.6, f"Confidence should be > 0.6, got {pattern.pattern_confidence}"
        
        logger.info(f"✅ Valid pattern test passed - confidence: {pattern.pattern_confidence:.3f}")
        
        # Test Case 2: Invalid pattern (not decreasing)
        invalid_bars = [0.1, 0.3, 0.5, 0.4]
        pattern = HistogramPattern(bars=invalid_bars)
        
        assert pattern.decreasing_sequence == False, "Should not detect decreasing sequence"
        assert pattern.pattern_confidence < 0.6, f"Confidence should be < 0.6, got {pattern.pattern_confidence}"
        
        logger.info(f"✅ Invalid pattern test passed - confidence: {pattern.pattern_confidence:.3f}")
        
        # Test Case 3: No momentum shift
        no_shift_bars = [0.5, 0.3, 0.1, 0.05]
        pattern = HistogramPattern(bars=no_shift_bars)
        
        assert pattern.momentum_shift == False, "Should not detect momentum shift"
        
        logger.info("✅ No momentum shift test passed")
    
    def test_squeeze_detection(self):
        """Test squeeze state detection"""
        logger.info("🧪 Testing Squeeze State Detection")
        
        # Create test data with squeeze condition
        df = self.create_test_data("squeeze_active")
        indicators = self.detector._calculate_indicators(df)
        
        if indicators:
            squeeze_state = self.detector._detect_squeeze_state(indicators)
            
            logger.info(f"✅ Squeeze detection test - Active: {squeeze_state.is_squeezed}, "
                       f"Duration: {squeeze_state.squeeze_duration}, "
                       f"Intensity: {squeeze_state.squeeze_intensity:.3f}")
        else:
            logger.warning("⚠️ Could not calculate indicators for squeeze test")
    
    def test_multi_timeframe_analysis(self):
        """Test multi-timeframe analysis"""
        logger.info("🧪 Testing Multi-Timeframe Analysis")
        
        # Create aligned patterns
        daily_bars = [0.5, 0.3, 0.1, 0.4]  # Valid bullish pattern
        weekly_bars = [0.6, 0.4, 0.2, 0.5]  # Valid bullish pattern
        
        daily_pattern = HistogramPattern(bars=daily_bars)
        weekly_pattern = HistogramPattern(bars=weekly_bars)
        
        multi_tf = MultiTimeframeAnalysis(
            daily_pattern=daily_pattern,
            weekly_pattern=weekly_pattern
        )
        
        assert multi_tf.timeframe_alignment == True, "Should detect timeframe alignment"
        assert multi_tf.trend_direction == "bullish", f"Should be bullish, got {multi_tf.trend_direction}"
        assert multi_tf.combined_confidence > 0.7, f"Combined confidence should be > 0.7, got {multi_tf.combined_confidence}"
        
        logger.info(f"✅ Multi-timeframe test passed - alignment: {multi_tf.timeframe_alignment}, "
                   f"direction: {multi_tf.trend_direction}, "
                   f"confidence: {multi_tf.combined_confidence:.3f}")
    
    async def test_full_pattern_detection(self):
        """Test complete pattern detection workflow"""
        logger.info("🧪 Testing Full Pattern Detection Workflow")
        
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        
        for symbol in test_symbols:
            logger.info(f"Testing pattern detection for {symbol}")
            
            # Create test data
            daily_data = self.create_test_data("valid_pattern")
            weekly_data = self.create_test_data("valid_pattern")
            
            # Run pattern detection
            result = await self.detector.detect_pattern(symbol, daily_data, weekly_data)
            
            if result:
                logger.info(f"✅ Pattern detected for {symbol}:")
                logger.info(f"   Direction: {result['direction']}")
                logger.info(f"   Confidence: {result['confidence']:.3f}")
                logger.info(f"   Signal Strength: {result['signal_strength']}")
                logger.info(f"   Entry: ${result['entry_price']:.2f}")
                logger.info(f"   Stop: ${result['stop_loss']:.2f}")
                logger.info(f"   Target: ${result['target_price']:.2f}")
                logger.info(f"   Risk/Reward: {result['risk_reward_ratio']:.2f}")
                
                # Validate result structure
                required_fields = [
                    'symbol', 'direction', 'confidence', 'signal_strength',
                    'entry_price', 'stop_loss', 'target_price', 'histogram_pattern'
                ]
                
                for field in required_fields:
                    assert field in result, f"Missing required field: {field}"
                
                logger.info(f"✅ Result validation passed for {symbol}")
            else:
                logger.info(f"❌ No pattern detected for {symbol}")
    
    async def test_integration_layer(self):
        """Test the integration layer with market engine"""
        logger.info("🧪 Testing Integration Layer")
        
        # Test without market engine (uses mock data)
        signal = await self.integrator.get_enhanced_ttm_signal("TEST")
        
        if signal:
            logger.info(f"✅ Integration test passed:")
            logger.info(f"   Symbol: {signal.symbol}")
            logger.info(f"   Direction: {signal.momentum_direction}")
            logger.info(f"   Strength: {signal.signal_strength}")
            logger.info(f"   Confidence: {signal.confidence:.3f}")
            logger.info(f"   Histogram: {signal.histogram_value:.4f}")
            logger.info(f"   Squeeze Active: {signal.squeeze_active}")
        else:
            logger.info("❌ Integration test failed - no signal generated")
    
    def test_parameter_configuration(self):
        """Test parameter configuration and updates"""
        logger.info("🧪 Testing Parameter Configuration")
        
        # Get current parameters
        params = self.detector.get_pattern_parameters()
        logger.info(f"Current parameters: {params}")
        
        # Update parameters
        self.detector.update_parameters(
            min_pattern_confidence=0.7,
            min_momentum_shift=0.002
        )
        
        # Verify updates
        updated_params = self.detector.get_pattern_parameters()
        assert updated_params['min_pattern_confidence'] == 0.7
        assert updated_params['min_momentum_shift'] == 0.002
        
        logger.info("✅ Parameter configuration test passed")
    
    def test_edge_cases(self):
        """Test edge cases and error handling"""
        logger.info("🧪 Testing Edge Cases")
        
        # Test with insufficient data
        small_df = pd.DataFrame({
            'open': [100, 101],
            'high': [102, 103],
            'low': [99, 100],
            'close': [101, 102],
            'volume': [1000, 1100]
        })
        
        indicators = self.detector._calculate_indicators(small_df)
        assert indicators is None, "Should return None for insufficient data"
        
        # Test with NaN values
        nan_bars = [0.5, np.nan, 0.1, 0.4]
        pattern = HistogramPattern(bars=nan_bars)
        # Should handle NaN gracefully
        
        # Test with empty data
        empty_df = pd.DataFrame()
        indicators = self.detector._calculate_indicators(empty_df)
        assert indicators is None, "Should return None for empty data"
        
        logger.info("✅ Edge cases test passed")
    
    async def run_all_tests(self):
        """Run all test cases"""
        logger.info("🚀 Starting TTM Pattern Detection Test Suite")
        logger.info("=" * 60)
        
        try:
            # Unit tests
            self.test_histogram_pattern_detection()
            self.test_squeeze_detection()
            self.test_multi_timeframe_analysis()
            self.test_parameter_configuration()
            self.test_edge_cases()
            
            # Integration tests
            await self.test_full_pattern_detection()
            await self.test_integration_layer()
            
            logger.info("=" * 60)
            logger.info("🎉 All tests completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            raise


async def main():
    """Run the test suite"""
    tester = TTMPatternTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
